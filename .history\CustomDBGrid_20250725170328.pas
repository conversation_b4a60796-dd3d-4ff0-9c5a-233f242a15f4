unit CustomDBGrid;

interface

uses
  Vcl.DBGrids, Vcl.Controls, System.Classes, Data.DB, Vcl.Grids, Vcl.StdCtrls,
  Vcl.Forms, System.SysUtils, Winapi.Windows, Winapi.Messages, Vcl.Graphics,
  Vcl.Buttons, LegendColorPalette, ColorPickerForm, Vcl.Dialogs, System.Types,
  EquipNeedRuleForm, ExceptionLogger;

type
  // Forward declaration
  TFormulaDBGrid = class;

  // 公式输入编辑器
  TFormulaInplaceEdit = class(TInplaceEdit)
  private
    FOriginalField: TField;
    FGrid: TFormulaDBGrid;
    FOriginalText: string;
    FIsFormula: Boolean;
    FBufferedValue: string;
    FColorButton: TSpeedButton;
    FButtonRect: TRect;  // 按钮区域
  protected
    procedure KeyPress(var Key: Char); override;
    procedure KeyDown(var Key: Word; Shift: TShiftState); override;
    procedure DoExit; override;
    procedure WMChar(var Message: TWMChar); message WM_CHAR;
    procedure MouseDown(Button: TMouseButton; Shift: TShiftState; X, Y: Integer); override;
    procedure WMPaint(var Message: TWMPaint); message WM_PAINT;
  public
    procedure SetBounds(ALeft, ATop, AWidth, AHeight: Integer); override;
  public
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
    procedure SetEditorColor(ColorValue: Integer);
    procedure UpdateColorPreview;
    procedure UpdateButtonRect;
    procedure DrawButton;
    procedure OpenColorPicker;
    procedure OpenEquipNeedRuleSelector;
    function IsColorField: Boolean;
    function IsNeedField: Boolean;
    function IsNeedLevelField: Boolean;
    function IsPointInButton(X, Y: Integer): Boolean;
    property OriginalField: TField read FOriginalField write FOriginalField;
    property Grid: TFormulaDBGrid read FGrid write FGrid;
    property BufferedValue: string read FBufferedValue write FBufferedValue;
  end;

  // 自定义DBGrid
  TFormulaDBGrid = class(TDBGrid)
  private
    FOnFormulaCalculate: TNotifyEvent;
    FAllowFormulaInput: Boolean;
    FCurrentEditor: TFormulaInplaceEdit;
    FCurrentColorValue: Integer;
    FShowingHeaderColor: Boolean;
    FDoubleClickEdit: Boolean; // 标志是否通过双击进入编辑模式
  protected
    function CreateEditor: TInplaceEdit; override;
    procedure KeyPress(var Key: Char); override;
    procedure KeyDown(var Key: Word; Shift: TShiftState); override;
    function CanEditShow: Boolean; override;
    function GetAlignmentFlag(Alignment: TAlignment): UINT;
    procedure CheckAndUpdateColorHeader;
    function DoMouseWheelDown(Shift: TShiftState; MousePos: TPoint): Boolean; override;
    function DoMouseWheelUp(Shift: TShiftState; MousePos: TPoint): Boolean; override;
    procedure WMChar(var Message: TWMChar); message WM_CHAR;
    procedure WMDelayedSingleClick(var Message: TMessage); message WM_USER + 100;
    function IsValidChar(InputChar: Char; Field: TField): Boolean; virtual;
    procedure DrawColumnCell(const Rect: TRect; DataCol: Integer; Column: TColumn; State: TGridDrawState); override;
    procedure DrawCell(ACol, ARow: Longint; ARect: TRect; AState: TGridDrawState); override;
    procedure DblClick; override;
    procedure MouseDown(Button: TMouseButton; Shift: TShiftState; X, Y: Integer); override;
    procedure MouseUp(Button: TMouseButton; Shift: TShiftState; X, Y: Integer); override;
    procedure ColEnter; override;
    procedure Click; override;
  public
    constructor Create(AOwner: TComponent); override;
    procedure UpdateColumnHeaderColor(ColorValue: Integer);
    procedure ClearColumnHeaderColor;
    procedure ResetEditingState;
    property OnFormulaCalculate: TNotifyEvent read FOnFormulaCalculate write FOnFormulaCalculate;
    property AllowFormulaInput: Boolean read FAllowFormulaInput write FAllowFormulaInput;
    property CurrentEditor: TFormulaInplaceEdit read FCurrentEditor;
  end;

implementation

{ TFormulaInplaceEdit }

constructor TFormulaInplaceEdit.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  FIsFormula := False;
  FBufferedValue := '';
  FColorButton := nil;
  FButtonRect := Rect(0, 0, 0, 0);
end;

destructor TFormulaInplaceEdit.Destroy;
begin
  if Assigned(FColorButton) then
    FColorButton.Free;
  inherited Destroy;
end;

// 设置编辑器颜色
procedure TFormulaInplaceEdit.SetEditorColor(ColorValue: Integer);
begin
  // 检查当前字段是否为颜色字段
  if Assigned(FGrid) and Assigned(FGrid.SelectedField) then
  begin
    // 判断是否是Color字段
    if (UpperCase(FGrid.SelectedField.FieldName) = 'COLOR') or
       (Pos('COLOR', UpperCase(FGrid.SelectedField.FieldName)) > 0) then
    begin
      // 更新DBGrid列标题的颜色预览
      FGrid.UpdateColumnHeaderColor(ColorValue);
    end;
  end;
end;

// 更新颜色预览
procedure TFormulaInplaceEdit.UpdateColorPreview;
var
  ColorValue: Integer;
  CurrentText: string;
begin
  if Assigned(FGrid) and Assigned(FGrid.SelectedField) then
  begin
    // 检查是否是Color字段
    if IsColorField then
    begin
      CurrentText := Text;
      // 尝试将文本转换为整数
      if TryStrToInt(CurrentText, ColorValue) then
      begin
        // 限制在0-255范围内
        if ColorValue < 0 then ColorValue := 0;
        if ColorValue > 255 then ColorValue := 255;

        // 更新列标题颜色
        FGrid.UpdateColumnHeaderColor(ColorValue);
      end;
    end;
  end;
end;

// 检查是否是Color字段
function TFormulaInplaceEdit.IsColorField: Boolean;
begin
  Result := Assigned(FGrid) and Assigned(FGrid.SelectedField) and
            ((UpperCase(FGrid.SelectedField.FieldName) = 'COLOR') or
             (Pos('COLOR', UpperCase(FGrid.SelectedField.FieldName)) > 0));
end;

// Check if current field is Need field
function TFormulaInplaceEdit.IsNeedField: Boolean;
begin
  Result := Assigned(FGrid) and Assigned(FGrid.SelectedField) and
            (UpperCase(FGrid.SelectedField.FieldName) = 'NEED');
end;

// Check if current field is NeedLevel field
function TFormulaInplaceEdit.IsNeedLevelField: Boolean;
begin
  Result := Assigned(FGrid) and Assigned(FGrid.SelectedField) and
            (UpperCase(FGrid.SelectedField.FieldName) = 'NEEDLEVEL');
end;

// 更新按钮区域
procedure TFormulaInplaceEdit.UpdateButtonRect;
var
  ButtonWidth, ButtonMargin: Integer;
begin
  if IsColorField or IsNeedField then
  begin
    // Use default values
    ButtonWidth := 25;
    ButtonMargin := 2;

    // Set button rectangle with configured size
    FButtonRect := Rect(Width - ButtonWidth - ButtonMargin, ButtonMargin,
                       Width - ButtonMargin, Height - ButtonMargin);
  end
  else
  begin
    // No button for other columns
    FButtonRect := Rect(0, 0, 0, 0);
  end;
end;

// 检查点击位置是否在按钮内
function TFormulaInplaceEdit.IsPointInButton(X, Y: Integer): Boolean;
begin
  Result := (IsColorField or IsNeedField) and
            (X >= FButtonRect.Left) and (X <= FButtonRect.Right) and
            (Y >= FButtonRect.Top) and (Y <= FButtonRect.Bottom);
end;

// 打开颜色选择器
procedure TFormulaInplaceEdit.OpenColorPicker;
var
  ColorIndex: Integer;
  CurrentText: string;
  OldText: string;
begin
  try
    CurrentText := Text;
    OldText := CurrentText; // Store original text

    // Parse current color index
    if TryStrToInt(CurrentText, ColorIndex) then
    begin
      if ColorIndex < 0 then ColorIndex := 0;
      if ColorIndex > 255 then ColorIndex := 255;
    end
    else
      ColorIndex := 0;

    // Show color picker dialog
    if TfrmColorPicker.ShowColorPicker(ColorIndex) then
    begin
      Text := IntToStr(ColorIndex);
      UpdateColorPreview;

      // If color changed, force save the value
      if Text <> OldText then
      begin
        FBufferedValue := Text;
        // Trigger formula calculation to save the value
        if Assigned(FGrid) and Assigned(FGrid.OnFormulaCalculate) then
          FGrid.OnFormulaCalculate(Self);
      end;
    end;
  except
    // Handle errors silently
  end;
end;

// Open equipment need rule selector
procedure TFormulaInplaceEdit.OpenEquipNeedRuleSelector;
var
  NeedCode: Integer;
  OldText: string;
begin
  try
    OldText := Text;
    NeedCode := StrToIntDef(Text, 0);

    // Show rule selector dialog
    if TfrmEquipNeedRule.ShowRuleSelector(NeedCode) then
    begin
      Text := IntToStr(NeedCode);

      // If value changed, force save
      if Text <> OldText then
      begin
        FBufferedValue := Text;
        // Trigger formula calculation to save the value
        if Assigned(FGrid) and Assigned(FGrid.OnFormulaCalculate) then
          FGrid.OnFormulaCalculate(Self);
      end;
    end;
  except
    // Handle errors silently
  end;
end;

// 鼠标按下事件
procedure TFormulaInplaceEdit.MouseDown(Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  if (Button = mbLeft) and IsPointInButton(X, Y) then
  begin
    if IsColorField then
      OpenColorPicker
    else if IsNeedField then
      OpenEquipNeedRuleSelector;
  end
  else
  begin
    // Normal mouse handling
    inherited MouseDown(Button, Shift, X, Y);
  end;
end;

// 绘制消息处理
procedure TFormulaInplaceEdit.WMPaint(var Message: TWMPaint);
begin
  inherited;

  // Draw button after inherited paint to avoid drawing issues
  if IsColorField or IsNeedField then
    DrawButton;
end;

// Separate button drawing method for better control
procedure TFormulaInplaceEdit.DrawButton;
var
  DC: HDC;
  Brush: HBRUSH;
  Pen: HPEN;
  ButtonText: string;
  TextRect: TRect;
  BgColor, BorderColor, TextColor: TColor;
  SavedDC: Integer;
begin
  UpdateButtonRect;

  if IsRectEmpty(FButtonRect) then Exit;

  DC := GetDC(Handle);
  try
    SavedDC := SaveDC(DC);
    try
      // Use default colors
      BgColor := RGB(245, 245, 245);
      BorderColor := RGB(100, 100, 100);
      TextColor := RGB(0, 0, 0);

      // Clear button area completely to prevent artifacts
      Brush := CreateSolidBrush(ColorToRGB(BgColor));
      FillRect(DC, FButtonRect, Brush);
      DeleteObject(Brush);

      // Draw button border
      Pen := CreatePen(PS_SOLID, 1, ColorToRGB(BorderColor));
      SelectObject(DC, Pen);
      Rectangle(DC, FButtonRect.Left, FButtonRect.Top, FButtonRect.Right, FButtonRect.Bottom);
      DeleteObject(Pen);

      // Draw button text
      SetBkMode(DC, TRANSPARENT);
      SetTextColor(DC, ColorToRGB(TextColor));

      ButtonText := '...';
      TextRect := FButtonRect;
      DrawText(DC, PChar(ButtonText), Length(ButtonText), TextRect,
        DT_CENTER or DT_VCENTER or DT_SINGLELINE);

    finally
      RestoreDC(DC, SavedDC);
    end;
  finally
    ReleaseDC(Handle, DC);
  end;
end;

// 重写SetBounds方法更新按钮位置
procedure TFormulaInplaceEdit.SetBounds(ALeft, ATop, AWidth, AHeight: Integer);
var
  ButtonWidth: Integer;
begin
  if IsColorField or IsNeedField then
  begin
    // Use default button width
    ButtonWidth := 30;

    // Reserve space for button
    inherited SetBounds(ALeft, ATop, AWidth - ButtonWidth, AHeight);
    UpdateButtonRect;
    // Redraw button after bounds change
    DrawButton;
  end
  else
  begin
    // Normal bounds for columns without buttons
    inherited SetBounds(ALeft, ATop, AWidth, AHeight);
    UpdateButtonRect;
  end;
end;



procedure TFormulaInplaceEdit.WMChar(var Message: TWMChar);
var
  Key: Char;
begin
  Key := Char(Message.CharCode);

  // 先调用KeyPress事件处理，然后调用inherited方法
  KeyPress(Key);
  Message.Result := 0;
end;

// 键盘按键事件处理，支持公式输入和特殊字符
procedure TFormulaInplaceEdit.KeyPress(var Key: Char);
begin
  case Key of
    #8: // Backspace
    begin
      if SelLength > 0 then
      begin
        // Clear selected text
        SelText := '';
      end
      else if SelStart > 0 then
      begin
        // Delete character before cursor
        SelStart := SelStart - 1;
        SelLength := 1;
        SelText := '';
      end;
      // Redraw button after text change
      if IsColorField or IsNeedField then
        DrawButton;
    end;
    #13: // Enter
    begin
      DoExit;
      inherited KeyPress(Key);
      // Enter键触发公式计算和数据更新
      if Assigned(FGrid) then
      begin
        Application.ProcessMessages;
        FGrid.Refresh;
        FGrid.Repaint;
      end;
    end;
    #27: // Escape
    begin
      Text := FOriginalText;
      inherited KeyPress(Key);
    end;
  else
    // 处理可见字符输入，包括*/=等运算符
    if Key >= ' ' then
    begin
      if (Text = '0') and CharInSet(Key, ['1'..'9']) then
      begin
        Text := Key;  // 设置为输入的字符
        SelStart := 1;  // 将光标移到字符后面
      end
      else
        SelText := Key;

      // Update color preview and redraw button
      UpdateColorPreview;
      if IsColorField or IsNeedField then
        DrawButton;
    end
    else
      inherited KeyPress(Key);
  end;
end;

procedure TFormulaInplaceEdit.KeyDown(var Key: Word; Shift: TShiftState);
begin
  case Key of
    VK_DELETE: // Delete??
    begin
      if SelLength > 0 then
      begin
        // 删除选中的文本
        SelText := '';
      end
      else if SelStart < Length(Text) then
      begin
        // 用SelText替换当前字符
        SelLength := 1;
        SelText := '';
      end;
      // 阻止调用inherited方法
      Key := 0; // 标记按键已被处理
    end;
    VK_RETURN:
    begin
      FBufferedValue := Text;
      FIsFormula := (Length(Text) > 1) and (Text[1] = '=');

      // 先处理其他按键，包括Enter键
      inherited KeyDown(Key, Shift);

      // 在Enter键后触发公式计算
      if Assigned(FGrid) and Assigned(FGrid.OnFormulaCalculate) then
        FGrid.OnFormulaCalculate(Self);
    end;
    VK_TAB:
    begin
      FBufferedValue := Text;
      FIsFormula := (Length(Text) > 1) and (Text[1] = '=');
      inherited KeyDown(Key, Shift);
    end;
    VK_ESCAPE:
    begin
      Text := FOriginalText;
      FBufferedValue := FOriginalText;
      FIsFormula := False;
      inherited KeyDown(Key, Shift);
    end;
  else
    inherited KeyDown(Key, Shift);
  end;
end;

procedure TFormulaInplaceEdit.DoExit;
var
  InputText: string;
begin
  InputText := Text;
  FBufferedValue := InputText;

  // 关键修复：确保数据真正提交到数据集
  if Assigned(FOriginalField) and Assigned(FOriginalField.DataSet) then
  begin
    try
      // 调试信息：显示当前数据集状态
      LogInfo('CustomDBGrid', Format(string('字段: %s, 原值: "%s", 新值: "%s", 数据集状态: %d'),
        [FOriginalField.FieldName, FOriginalField.AsString, InputText, Ord(FOriginalField.DataSet.State)]), string('数据编辑调试'));
      
      // 进入编辑状态
      if not (FOriginalField.DataSet.State in [dsEdit, dsInsert]) then
      begin
        FOriginalField.DataSet.Edit;
        LogInfo('CustomDBGrid', string('数据集已进入编辑状态'), string('数据编辑调试'));
      end;
      
      // 更新字段值
      if FOriginalField.AsString <> InputText then
      begin
        FOriginalField.AsString := InputText;
        LogInfo('CustomDBGrid', Format('字段值已更新: %s = "%s"', [FOriginalField.FieldName, InputText]), '数据编辑调试');

        // 立即提交更改
        if FOriginalField.DataSet.State in [dsEdit, dsInsert] then
        begin
          FOriginalField.DataSet.Post;
          LogInfo('CustomDBGrid', string('数据已提交到数据集'), string('数据编辑调试'));
        end;
      end
      else
      begin
        LogInfo('CustomDBGrid', string('字段值未改变，无需保存'), string('数据编辑调试'));
      end;
    except
      on E: Exception do
      begin
        // 如果提交失败，取消编辑
        if FOriginalField.DataSet.State in [dsEdit, dsInsert] then
          FOriginalField.DataSet.Cancel;
        LogAndShowError('CustomDBGrid', '保存数据失败', E, '数据编辑', True);
      end;
    end;
  end
  else
  begin
    LogAndShowWarning('CustomDBGrid', string('字段或数据集未分配'), string('数据编辑调试'));
  end;

  // Check if it's a formula
  FIsFormula := (Length(InputText) > 1) and (InputText[1] = '=');

  inherited DoExit;

  // 退出编辑时清理颜色预览和刷新
  if Assigned(FGrid) then
  begin
    // 清理颜色预览
    FGrid.ClearColumnHeaderColor;

    // 重置双击编辑标志，回到只读模式
    FGrid.FDoubleClickEdit := False;
    FGrid.ReadOnly := True;
    FGrid.Options := FGrid.Options - [dgEditing];
    if Assigned(FGrid.DataSource) then
      FGrid.DataSource.AutoEdit := False;

    // 触发公式计算事件
    if Assigned(FGrid.OnFormulaCalculate) then
      FGrid.OnFormulaCalculate(Self);

    // 刷新网格显示
    FGrid.Refresh;
  end;
end;

{ TFormulaDBGrid }

constructor TFormulaDBGrid.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  FAllowFormulaInput := True;
  FCurrentEditor := nil;
  FCurrentColorValue := 0;
  FShowingHeaderColor := False;
  FDoubleClickEdit := False;
  // 设置网格选项，不包含dgEditing以防止默认编辑
  Options := [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack];
  // 默认为只读模式
  ReadOnly := True;
end;

// 更新列标题颜色预览
procedure TFormulaDBGrid.UpdateColumnHeaderColor(ColorValue: Integer);
begin
  FCurrentColorValue := ColorValue;
  FShowingHeaderColor := True;

  // 刷新标题行
  InvalidateRow(0); // 重绘标题行
end;

// 清除列标题颜色
procedure TFormulaDBGrid.ClearColumnHeaderColor;
begin
  FShowingHeaderColor := False;
  FCurrentColorValue := 0;

  // 刷新标题行以清除颜色
  InvalidateRow(0);
end;

function TFormulaDBGrid.CreateEditor: TInplaceEdit;
var
  FormulaEditor: TFormulaInplaceEdit;
begin
  FormulaEditor := TFormulaInplaceEdit.Create(Self);
  FormulaEditor.OriginalField := SelectedField;
  FormulaEditor.Grid := Self;
  FCurrentEditor := FormulaEditor;

  Result := FormulaEditor;
end;

function TFormulaDBGrid.CanEditShow: Boolean;
begin
  Result := inherited CanEditShow and FAllowFormulaInput and FDoubleClickEdit;
end;

// 鼠标按下事件，控制单击行为
procedure TFormulaDBGrid.MouseDown(Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  // 只允许选择单元格，不进入编辑模式
  if Button = mbLeft then
  begin
    // 调用父类方法进行正常的单元格选择
    inherited MouseDown(Button, Shift, X, Y);

    // 延迟处理单击逻辑，避免干扰双击检测
    // 使用PostMessage延迟执行，让双击事件有机会先触发
    PostMessage(Handle, WM_USER + 100, 0, 0);
  end
  else
  begin
    // 对于非左键点击，使用默认处理
    inherited MouseDown(Button, Shift, X, Y);
  end;
end;

// 鼠标释放事件，处理Color字段点击
procedure TFormulaDBGrid.MouseUp(Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
  ColorValue: Integer;
  DataCol: Integer;
  Column: TColumn;
begin
  inherited MouseUp(Button, Shift, X, Y);

  // 检查是否点击了数据行
  if (Row > 0) and (Col >= IndicatorOffset) then
  begin
    // 计算实际的数据列索引
    DataCol := Col - IndicatorOffset;
    if (DataCol >= 0) and (DataCol < Columns.Count) then
    begin
      Column := Columns[DataCol];
      if Assigned(Column.Field) and
         ((UpperCase(Column.Field.FieldName) = 'COLOR') or
          (Pos('COLOR', UpperCase(Column.Field.FieldName)) > 0)) and
         not Column.Field.IsNull then
      begin
        // 尝试将Color字段值转换为整数并显示颜色
        if TryStrToInt(Column.Field.AsString, ColorValue) then
          // 转换成功，使用转换后的值
        else
          ColorValue := 0; // 转换失败，使用默认值
        UpdateColumnHeaderColor(ColorValue);
      end
      else
      begin
        // 如果不是Color字段，清除Color预览
        ClearColumnHeaderColor;
      end;
    end;
  end
  else
  begin
    // 如果点击了标题行或其他区域，清除Color预览
    ClearColumnHeaderColor;
  end;
end;

// 列进入事件，处理Color字段预览
procedure TFormulaDBGrid.ColEnter;
begin
  inherited ColEnter;
  CheckAndUpdateColorHeader;
end;

// 键盘按下事件，特殊处理Enter键和编辑键
procedure TFormulaDBGrid.KeyDown(var Key: Word; Shift: TShiftState);
begin
  case Key of
    VK_ESCAPE:
    begin
      // Escape键退出编辑模式
      if EditorMode and FDoubleClickEdit then
      begin
        try
          if Assigned(DataSource) and Assigned(DataSource.DataSet) and
             (DataSource.DataSet.State in [dsEdit, dsInsert]) then
            DataSource.DataSet.Cancel;
        except
          // 忽略取消编辑时的错误
        end;
        FDoubleClickEdit := False;
        Key := 0; // 阻止进一步处理
        Exit;
      end
      else
      begin
        inherited KeyDown(Key, Shift);
      end;
    end;
    VK_RETURN:
    begin
      // Enter键处理：在编辑模式下确认编辑，否则正常处理
      if EditorMode and FDoubleClickEdit then
      begin
        try
          if Assigned(DataSource) and Assigned(DataSource.DataSet) and
             (DataSource.DataSet.State in [dsEdit, dsInsert]) then
            DataSource.DataSet.Post;
        except
          // 忽略提交编辑时的错误
        end;
        FDoubleClickEdit := False;
        Key := 0; // 阻止进一步处理
        Exit;
      end
      else
      begin
        // 先处理标准的Enter键逻辑，设置编辑器并退出
        inherited KeyDown(Key, Shift);

        // 然后在编辑器退出后触发公式计算
        if Assigned(FCurrentEditor) and Assigned(OnFormulaCalculate) then
        begin
          OnFormulaCalculate(FCurrentEditor);
        end;
      end;
    end;
    VK_F2:
    begin
      // F2键进入编辑模式
      if FAllowFormulaInput and Assigned(SelectedField) and ReadOnly then
      begin
        FDoubleClickEdit := True;
        ReadOnly := False;
        Options := Options + [dgEditing];
        if Assigned(DataSource) then
          DataSource.AutoEdit := True;
        EditorMode := True;
        CheckAndUpdateColorHeader;
      end
      else
      begin
        inherited KeyDown(Key, Shift);
      end;
    end;
    VK_TAB:
    begin
      // Tab键只导航，不进入编辑模式
      FDoubleClickEdit := False;
      inherited KeyDown(Key, Shift);
    end;
  else
    inherited KeyDown(Key, Shift);
  end;

  // 处理导航键时更新Color字段预览，并确保不进入编辑模式
  if Key in [VK_UP, VK_DOWN, VK_LEFT, VK_RIGHT, VK_PRIOR, VK_NEXT, VK_HOME, VK_END, VK_TAB] then
  begin
    // 导航时重置编辑标志
    if Key <> VK_TAB then // Tab键已经在上面处理了
      FDoubleClickEdit := False;

    // 处理消息队列中的事件
    Application.ProcessMessages;
    CheckAndUpdateColorHeader;
  end;
end;

// 检查并更新Color字段标题预览
procedure TFormulaDBGrid.CheckAndUpdateColorHeader;
var
  ColorValue: Integer;
begin
  // 检查当前选中字段是否为Color字段
  if Assigned(SelectedField) and (Row > 0) then
  begin
    if ((UpperCase(SelectedField.FieldName) = 'COLOR') or
        (Pos('COLOR', UpperCase(SelectedField.FieldName)) > 0)) and
       not SelectedField.IsNull then
    begin
      // 尝试将Color字段值转换为整数并显示颜色
      if TryStrToInt(SelectedField.AsString, ColorValue) then
        // 转换成功，使用转换后的值
      else
        ColorValue := 0; // 转换失败，使用默认值
      UpdateColumnHeaderColor(ColorValue);
    end
    else
    begin
      // 如果不是Color字段，清除Color预览
      ClearColumnHeaderColor;
    end;
  end
  else
  begin
    // 如果没有选中字段或在标题行，清除Color预览
    ClearColumnHeaderColor;
  end;
end;

// 单击事件处理 - 确保单击只选择，不进入编辑模式
procedure TFormulaDBGrid.Click;
begin
  // 如果当前在编辑模式且不是双击编辑，退出编辑模式
  if EditorMode and not FDoubleClickEdit then
  begin
    try
      if Assigned(DataSource) and Assigned(DataSource.DataSet) and
         (DataSource.DataSet.State in [dsEdit, dsInsert]) then
        DataSource.DataSet.Cancel;
    except
      // 忽略取消编辑时的错误
    end;
  end;

  // 重置双击编辑标志（单击后重置）
  FDoubleClickEdit := False;

  // 调用父类的Click处理
  inherited Click;
end;

// 双击事件处理 - 进入编辑模式
procedure TFormulaDBGrid.DblClick;
var
  IsEditableDataSource: Boolean;
begin
  // 检查是否是可编辑的数据源
  IsEditableDataSource := Assigned(DataSource) and Assigned(DataSource.DataSet) and
                         DataSource.DataSet.Active and
                         ((DataSource.DataSet.ClassName = 'TFDMemTable') or
                          (DataSource.DataSet.ClassName = 'TFDQuery'));

  // 双击时启用编辑模式
  if FAllowFormulaInput and Assigned(SelectedField) and IsEditableDataSource then
  begin
    // 设置双击编辑标志
    FDoubleClickEdit := True;

    // 启用编辑模式
    ReadOnly := False;
    Options := Options + [dgEditing];
    if Assigned(DataSource) then
      DataSource.AutoEdit := True;

    // 确保数据集处于可编辑状态
    if Assigned(DataSource.DataSet) and DataSource.DataSet.Active then
    begin
      try
        if not (DataSource.DataSet.State in [dsEdit, dsInsert]) then
          DataSource.DataSet.Edit;
      except
        // 如果无法进入编辑状态，重置标志
        FDoubleClickEdit := False;
        Exit;
      end;
    end;

    // 调用父类的DblClick，这会触发编辑模式
    inherited DblClick;

    // 确保进入编辑模式
    if not EditorMode then
      EditorMode := True;

    // 检查并更新Color字段预览
    CheckAndUpdateColorHeader;
  end
  else
  begin
    inherited DblClick;
  end;
end;


// 重写DBGrid的KeyPress方法
procedure TFormulaDBGrid.KeyPress(var Key: Char);
begin
  // 只有在已经处于编辑模式时才允许字符输入
  if EditorMode and FDoubleClickEdit then
  begin
    inherited KeyPress(Key);
  end
  else
  begin
    // 阻止默认的字符输入处理，避免单击后直接输入字符进入编辑模式
    Key := #0; // 标记按键已被处理
  end;
end;



function TFormulaDBGrid.DoMouseWheelDown(Shift: TShiftState; MousePos: TPoint): Boolean;
begin
  Result := inherited DoMouseWheelDown(Shift, MousePos);
end;

function TFormulaDBGrid.DoMouseWheelUp(Shift: TShiftState; MousePos: TPoint): Boolean;
begin
  Result := inherited DoMouseWheelUp(Shift, MousePos);
end;

// 重写DBGrid的WMChar消息
procedure TFormulaDBGrid.WMChar(var Message: TWMChar);
begin
  // 阻止默认的字符消息处理
  Message.Result := 0;
end;

function TFormulaDBGrid.IsValidChar(InputChar: Char; Field: TField): Boolean;
begin
  Result := True;
end;

// 获取对齐方式的DrawText标志
function TFormulaDBGrid.GetAlignmentFlag(Alignment: TAlignment): UINT;
begin
  case Alignment of
    taLeftJustify: Result := DT_LEFT;
    taRightJustify: Result := DT_RIGHT;
    taCenter: Result := DT_CENTER;
  else
    Result := DT_LEFT;
  end;
end;

// 重写DrawColumnCell方法以支持颜色字段显示
procedure TFormulaDBGrid.DrawColumnCell(const Rect: TRect; DataCol: Integer; Column: TColumn; State: TGridDrawState);
var
  ColorValue: Integer;
  CellColor: TColor;
  TextColor: TColor;
  CellText: string;
  IsCurrentEditingCell: Boolean;
begin
  // 检查是否为当前编辑的单元格
  IsCurrentEditingCell := EditorMode and (Col = DataCol + IndicatorOffset) and (Row > 0);

  // 检查是否是Color字段
  if Assigned(Column.Field) and
     ((UpperCase(Column.Field.FieldName) = 'COLOR') or
      (Pos('COLOR', UpperCase(Column.Field.FieldName)) > 0)) and
     not Column.Field.IsNull then
  begin
    if TryStrToInt(Column.Field.AsString, ColorValue) then
      // 转换成功，使用转换后的值
    else
      ColorValue := 0; // 转换失败，使用默认值
    if ColorValue >= 0 then
    begin
      // 限制在0-255范围内
      if ColorValue > 255 then ColorValue := 255;

      // 如果是当前编辑的单元格，跳过自定义绘制
      if IsCurrentEditingCell then
      begin
        inherited DrawColumnCell(Rect, DataCol, Column, State);
        Exit;
      end;

      // 根据颜色值获取对应的颜色
      CellColor := GetLegendColor(ColorValue);

      // 根据颜色亮度选择文本颜色
      if ColorValue < 128 then
        TextColor := clWhite
      else
        TextColor := clBlack;

      // 设置背景色
      Canvas.Brush.Color := CellColor;
      Canvas.FillRect(Rect);

      // 设置文本色
      Canvas.Font.Color := TextColor;
      CellText := IntToStr(ColorValue);
      Canvas.TextRect(Rect, Rect.Left + 2, Rect.Top + 2, CellText);

      Exit; // 跳过调用inherited的默认绘制
    end;
  end;

  // 对于非颜色字段，使用默认绘制
  inherited DrawColumnCell(Rect, DataCol, Column, State);
end;

// 重写DrawCell方法以支持自定义单元格绘制
procedure TFormulaDBGrid.DrawCell(ACol, ARow: Longint; ARect: TRect; AState: TGridDrawState);
var
  CellColor: TColor;
  TextColor: TColor;
  Column: TColumn;
  DataCol: Integer;
  IsSelected: Boolean;
  IsCurrentCell: Boolean;
begin
  // 检查数据源是否有效
  if not Assigned(DataSource) or not Assigned(DataSource.DataSet) or
     not DataSource.DataSet.Active then
  begin
    // 如果数据源无效，使用默认绘制
    inherited DrawCell(ACol, ARow, ARect, AState);
    Exit;
  end;

  // 计算选中状态和当前单元格状态
  IsSelected := (ARow = Row) and (ARow > 0); // 是否选中行
  IsCurrentCell := (ACol = Col) and (ARow = Row) and (ARow > 0); // 是否当前单元格

  // 处理标题行（第0行）
  if ARow = 0 then
  begin
    DataCol := ACol - IndicatorOffset;

    // 如果正在显示Color字段预览且是有效列
    if FShowingHeaderColor and (DataCol >= 0) and (DataCol < Columns.Count) then
    begin
      Column := Columns[DataCol];
      if Assigned(Column.Field) and
         ((UpperCase(Column.Field.FieldName) = 'COLOR') or
          (Pos('COLOR', UpperCase(Column.Field.FieldName)) > 0)) then
      begin
        // 限制颜色值在0-255范围内
        if FCurrentColorValue < 0 then FCurrentColorValue := 0;
        if FCurrentColorValue > 255 then FCurrentColorValue := 255;

        // 获取对应的颜色
        CellColor := GetLegendColor(FCurrentColorValue);

        // 根据颜色亮度选择文本颜色
        if FCurrentColorValue < 128 then
          TextColor := clWhite
        else
          TextColor := clBlack;

        // 绘制Color字段的标题背景
        Canvas.Brush.Color := CellColor;
        Canvas.FillRect(ARect);

        // 绘制边框
        Canvas.Pen.Color := clGray;
        Canvas.Rectangle(ARect);

        // 设置标题文本的字体和颜色
        Canvas.Font.Color := TextColor;
        Canvas.Font.Style := [fsBold];

        // 绘制标题文本
        Canvas.TextRect(ARect, ARect.Left + 4, ARect.Top + 4, Column.Title.Caption);

        Exit; // Color字段标题绘制完成，退出
      end;
    end;

    // 如果不是Color字段但是当前选中列的标题
    if (ACol = Col) and (Row > 0) then
    begin
      // 绘制选中列标题的特殊背景
      Canvas.Brush.Color := RGB(173, 216, 230); // 浅蓝色
      Canvas.FillRect(ARect);

      // 绘制边框
      Canvas.Pen.Color := clGray;
      Canvas.Rectangle(ARect);

      // 设置标题文本颜色
      Canvas.Font.Color := clBlack;
      Canvas.Font.Style := [fsBold];

      if (DataCol >= 0) and (DataCol < Columns.Count) then
      begin
        Column := Columns[DataCol];
        Canvas.TextRect(ARect, ARect.Left + 4, ARect.Top + 4, Column.Title.Caption);
      end;

      Exit;
    end;
  end
  // 处理数据行
  else if ARow > 0 then
  begin

    // 处理选中行的特殊绘制，特别是Color字段的颜色显示
    if IsSelected then
    begin
      // 如果处于编辑模式，需要特殊处理
      if EditorMode then
      begin
        // 对于非当前编辑单元格的选中行单元格
        if not IsCurrentCell then
        begin
          // 检查是否是Color字段，Color字段需要特殊的颜色显示
          DataCol := ACol - IndicatorOffset;
          if (DataCol >= 0) and (DataCol < Columns.Count) then
          begin
            Column := Columns[DataCol];
            if Assigned(Column.Field) and
               ((UpperCase(Column.Field.FieldName) = 'COLOR') or
                (Pos('COLOR', UpperCase(Column.Field.FieldName)) > 0)) and
               not Column.Field.IsNull then
            begin
              // Color字段使用专门的DrawColumnCell方法绘制
              DrawColumnCell(ARect, DataCol, Column, AState);
              Exit;
            end;
          end;

          // 非Color字段的选中行单元格绘制
          Canvas.Brush.Color := RGB(240, 248, 255); // 浅蓝色背景表示选中但非编辑
          Canvas.FillRect(ARect);

          // 绘制边框
          Canvas.Pen.Color := clGray;
          Canvas.Rectangle(ARect);

          Canvas.Font.Color := clBlack; // 设置文本为黑色
          Canvas.Font.Style := [];

          // 绘制单元格文本内容
          if (DataCol >= 0) and (DataCol < Columns.Count) then
          begin
            Column := Columns[DataCol];
            if Assigned(Column.Field) and not Column.Field.IsNull then
            begin
              // 根据Column的对齐方式绘制文本
              var TextRect: TRect;
              TextRect := ARect;
              TextRect.Left := TextRect.Left + 4; // 左边距
              TextRect.Right := TextRect.Right - 4; // 右边距
              DrawText(Canvas.Handle, PChar(Column.Field.AsString), -1, TextRect,
                       DT_SINGLELINE or DT_VCENTER or GetAlignmentFlag(Column.Alignment));
            end;
          end;

          Exit;
        end;
      end
      else
      begin
        // 非编辑模式下的选中行单元格绘制
        // 计算数据列索引
        DataCol := ACol - IndicatorOffset;

        if IsCurrentCell then
        begin
          // 当前单元格使用更深的蓝色背景
          Canvas.Brush.Color := RGB(200, 230, 255); // 深蓝色
          Canvas.Font.Color := clBlack;  // 设置文本为黑色
        end
        else
        begin
          // 选中行的其他单元格使用浅蓝色
          Canvas.Brush.Color := RGB(240, 248, 255); // 浅蓝色背景
          Canvas.Font.Color := clBlack; // 黑色文本
        end;

        Canvas.FillRect(ARect);

        // 绘制边框
        Canvas.Pen.Color := clGray;
        Canvas.Rectangle(ARect);

        Canvas.Font.Style := [];

        // 绘制单元格文本内容
        if (DataCol >= 0) and (DataCol < Columns.Count) then
        begin
          Column := Columns[DataCol];
          if Assigned(Column.Field) and not Column.Field.IsNull then
          begin
            // 根据Column的对齐方式绘制文本
            var TextRect2: TRect;
            TextRect2 := ARect;
            TextRect2.Left := TextRect2.Left + 4; // 左边距
            TextRect2.Right := TextRect2.Right - 4; // 右边距
            DrawText(Canvas.Handle, PChar(Column.Field.AsString), -1, TextRect2,
                     DT_SINGLELINE or DT_VCENTER or GetAlignmentFlag(Column.Alignment));
          end;
        end;

        Exit;
      end;
    end;
  end;

  // 对于其他情况，使用默认绘制
  inherited DrawCell(ACol, ARow, ARect, AState);
end;

// 处理延迟的单击事件
procedure TFormulaDBGrid.WMDelayedSingleClick(var Message: TMessage);
begin
  // 延迟单击事件主要用于区分单击和双击
  // 如果不是双击编辑模式，确保退出编辑状态
  if not FDoubleClickEdit and EditorMode then
  begin
    try
      if Assigned(DataSource) and Assigned(DataSource.DataSet) and
         (DataSource.DataSet.State in [dsEdit, dsInsert]) then
        DataSource.DataSet.Cancel;
    except
      // 忽略取消编辑时的错误
    end;
  end;
end;

// 重置编辑状态 - 在数据库切换时调用
procedure TFormulaDBGrid.ResetEditingState;
begin
  // 暂时简化此方法以排查问题
  FDoubleClickEdit := False;
  ReadOnly := True;
end;

end.
