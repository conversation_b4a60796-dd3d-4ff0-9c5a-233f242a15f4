unit CustomDBGrid;

interface

uses
  System.SysUtils, System.Classes, System.Types, System.UITypes,
  Vcl.Controls, Vcl.DBGrids, Vcl.Graphics, Vcl.StdCtrls, Vcl.ExtCtrls,
  Data.DB, Winapi.Windows, Winapi.Messages;

type
  // 前向声明
  TFormulaDBGrid = class;

  // 自定义内置编辑器
  TFormulaInplaceEdit = class(TInplaceEdit)
  private
    FGrid: TFormulaDBGrid;
    FOriginalField: TField;
    FOriginalText: string;
    FBufferedValue: string;
    FIsFormula: Boolean;
  protected
    procedure WMChar(var Message: TWMChar); message WM_CHAR;
    procedure KeyPress(var Key: Char); override;
    procedure KeyDown(var Key: Word; Shift: TShiftState); override;
    procedure DoExit; override;
  public
    property Grid: TFormulaDBGrid read FGrid write FGrid;
    property OriginalField: TField read FOriginalField write FOriginalField;
    property BufferedValue: string read FBufferedValue write FBufferedValue;
    property IsFormula: Boolean read FIsFormula write FIsFormula;
  end;

  // 自定义DBGrid
  TFormulaDBGrid = class(TDBGrid)
  private
    FOnFormulaCalculate: TNotifyEvent;
    FAllowFormulaInput: Boolean;
    FCurrentEditor: TFormulaInplaceEdit;
    FCurrentColorValue: Integer;
  protected
    function CreateEditor: TInplaceEdit; override;
    procedure KeyPress(var Key: Char); override;
    procedure KeyDown(var Key: Word; Shift: TShiftState); override;
    function CanEditShow: Boolean; override;
    procedure DblClick; override;
    procedure MouseDown(Button: TMouseButton; Shift: TShiftState; X, Y: Integer); override;
    procedure MouseUp(Button: TMouseButton; Shift: TShiftState; X, Y: Integer); override;
    procedure ColEnter; override;
  public
    constructor Create(AOwner: TComponent); override;
    procedure UpdateColumnHeaderColor(ColorValue: Integer);
    procedure ClearColumnHeaderColor;
    property OnFormulaCalculate: TNotifyEvent read FOnFormulaCalculate write FOnFormulaCalculate;
    property AllowFormulaInput: Boolean read FAllowFormulaInput write FAllowFormulaInput;
    property CurrentEditor: TFormulaInplaceEdit read FCurrentEditor;
  end;

implementation

{ TFormulaInplaceEdit }

procedure TFormulaInplaceEdit.WMChar(var Message: TWMChar);
begin
  inherited;
end;

procedure TFormulaInplaceEdit.KeyPress(var Key: Char);
begin
  inherited KeyPress(Key);
end;

procedure TFormulaInplaceEdit.KeyDown(var Key: Word; Shift: TShiftState);
begin
  inherited KeyDown(Key, Shift);
end;

procedure TFormulaInplaceEdit.DoExit;
begin
  inherited DoExit;
end;

{ TFormulaDBGrid }

constructor TFormulaDBGrid.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  FAllowFormulaInput := True;
  FCurrentEditor := nil;
  FCurrentColorValue := 0;
  // 设置网格选项
  Options := [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack];
  // 默认为只读模式
  ReadOnly := True;
end;

procedure TFormulaDBGrid.UpdateColumnHeaderColor(ColorValue: Integer);
begin
  FCurrentColorValue := ColorValue;
end;

procedure TFormulaDBGrid.ClearColumnHeaderColor;
begin
  FCurrentColorValue := 0;
end;

function TFormulaDBGrid.CreateEditor: TInplaceEdit;
var
  FormulaEditor: TFormulaInplaceEdit;
begin
  FormulaEditor := TFormulaInplaceEdit.Create(Self);
  FormulaEditor.OriginalField := SelectedField;
  FormulaEditor.Grid := Self;
  FCurrentEditor := FormulaEditor;
  Result := FormulaEditor;
end;

function TFormulaDBGrid.CanEditShow: Boolean;
begin
  Result := inherited CanEditShow and FAllowFormulaInput;
end;

procedure TFormulaDBGrid.ColEnter;
begin
  inherited ColEnter;
end;

procedure TFormulaDBGrid.MouseDown(Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  inherited MouseDown(Button, Shift, X, Y);
end;

procedure TFormulaDBGrid.MouseUp(Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  inherited MouseUp(Button, Shift, X, Y);
end;

procedure TFormulaDBGrid.KeyDown(var Key: Word; Shift: TShiftState);
begin
  case Key of
    VK_RETURN:
    begin
      inherited KeyDown(Key, Shift);
      if Assigned(FCurrentEditor) and Assigned(OnFormulaCalculate) then
        OnFormulaCalculate(FCurrentEditor);
    end;
    VK_F2:
    begin
      if not EditorMode then
        EditorMode := True;
      inherited KeyDown(Key, Shift);
    end;
  else
    inherited KeyDown(Key, Shift);
  end;
end;

procedure TFormulaDBGrid.DblClick;
begin
  inherited DblClick;
end;

procedure TFormulaDBGrid.KeyPress(var Key: Char);
begin
  inherited KeyPress(Key);
end;

end.
