unit DBConnection;

interface

uses
  System.SysUtils, System.Classes, Vcl.ComCtrls, Data.DB, System.Variants, System.TypInfo,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Phys.ODBC, FireDAC.Phys.ODBCDef,
  System.Generics.Collections, ExceptionLogger,
  {$IFDEF MSWINDOWS}
  {$IFDEF BDE_AVAILABLE}
  BDE, DBTables,
  {$ENDIF}
  {$ENDIF}
  System.IOUtils, System.StrUtils, System.Math, Vcl.Forms, Vcl.Dialogs, Winapi.Windows,
  UnifiedExcelProcessor_POI;

type
  TDBType = (dbtSQLite, dbtAccess, dbtParadox, dbtParadoxBDE, dbtParadoxDirect, dbtExcel);

  TDBConnectionManager = class
  private
    FConnection: TFDConnection;
    FTreeView: TTreeView;
    FDBType: TDBType;
    FConnectionInfo: string;
    FLastError: string;
    FCurrentExcelFile: string;

    function ExecuteQuery(const ASQL: string): TFDQuery;
    function IsSQLite: Boolean;
    function IsAccess: Boolean;
    function IsParadox: Boolean;
    function IsExcel: Boolean;

    function WriteCSVFile(const FileName: string; Data: TFDMemTable; HasHeader: Boolean = True): Boolean;
    procedure RefreshDBTypeDetection;

  public
    constructor Create(AConnection: TFDConnection; ATreeView: TTreeView);
    destructor Destroy; override;
    procedure LoadTables;
    procedure LoadViews;
    procedure LoadIndices;
    procedure LoadTriggers;
    function GetTableStructure(const ATableName: string): TFDQuery;
    function GetTableData(const ATableName: string): TFDQuery;
    function ExecuteSQL(const ASQL: string): TFDQuery;
    function GetDatabaseInfo: TStringList;
    function GetConnectionInfo: string;
    function GetTableNames: TStringList;

    // 数据库连接管理
    function ConnectToDatabase(const APath, AName: string; ADBType: TDBType): Boolean;
    procedure DisconnectFromDatabase;

    // Excel文件操作
    function ConnectToExcelFile(const AFilePath: string): Boolean;
    function GetExcelSheetNames: TStringList;
    function LoadExcelSheetData(const ASheetName: string): TFDMemTable;
    procedure DisconnectFromExcel;

    property DBType: TDBType read FDBType;
    property LastError: string read FLastError;
    property CurrentExcelFile: string read FCurrentExcelFile;
  end;

implementation

constructor TDBConnectionManager.Create(AConnection: TFDConnection; ATreeView: TTreeView);
var
  DriverName: string;
  ConnectionString: string;
begin
  FConnection := AConnection;
  FTreeView := ATreeView;
  FConnectionInfo := '';
  FLastError := '';
  FCurrentExcelFile := '';

  // 根据连接参数确定数据库类型
  if Assigned(FConnection) then
  begin
    if FConnection.Params.DriverID = 'SQLite' then
    begin
      // 检查是否是Excel模式
      if FConnection.Params.Values['ExcelMode'] = '1' then
      begin
        FDBType := dbtExcel;
        FConnectionInfo := '数据源: Excel文件 (XLS格式)';
      end
      else
      begin
        FDBType := dbtSQLite;
        FConnectionInfo := '数据源: FireDAC SQLite 数据库驱动';
      end;
    end
    else if FConnection.Params.DriverID = 'MSAcc' then
    begin
      FDBType := dbtAccess;
      FConnectionInfo := '数据源: FireDAC MSAcc 数据库驱动 (Microsoft Access)';
    end
    else if FConnection.Params.DriverID = 'ODBC' then
    begin
      // 通过ODBC驱动名称判断具体的数据库类型
      if (Pos('Access', FConnection.Params.Values['DriverName']) > 0) or
         (Pos('*.mdb', FConnection.Params.Values['DriverName']) > 0) or
         (Pos('*.accdb', FConnection.Params.Values['DriverName']) > 0) then
      begin
        FDBType := dbtAccess;
        FConnectionInfo := '数据源: ODBC 驱动 (Microsoft Access)';
      end
      else if (Pos('Paradox', FConnection.Params.Values['DriverName']) > 0) or
              (Pos('*.db', FConnection.Params.Values['DriverName']) > 0) then
      begin
        FDBType := dbtParadox;
        FConnectionInfo := '数据源: ODBC 驱动 (Paradox)';
      end
      else
      begin
        FDBType := dbtSQLite; // 默认SQLite
        FConnectionInfo := '数据源: ODBC 驱动 (SQLite)';
      end;
    end
    else
    begin
      FDBType := dbtSQLite; // 默认SQLite
      FConnectionInfo := '数据源: 未知驱动类型';
    end;
  end
  else
  begin
    FDBType := dbtSQLite; // 默认SQLite
    FConnectionInfo := '数据源: 未连接';
  end;
end;

function TDBConnectionManager.IsSQLite: Boolean;
begin
  Result := (FDBType = dbtSQLite);
end;

function TDBConnectionManager.IsAccess: Boolean;
begin
  Result := (FDBType = dbtAccess);
end;

function TDBConnectionManager.IsParadox: Boolean;
begin
  Result := (FDBType = dbtParadox);
end;

function TDBConnectionManager.IsExcel: Boolean;
begin
  Result := (FDBType = dbtExcel);
end;

function TDBConnectionManager.GetConnectionInfo: string;
begin
  Result := FConnectionInfo;
end;

destructor TDBConnectionManager.Destroy;
begin
  inherited;
end;

function TDBConnectionManager.ExecuteQuery(const ASQL: string): TFDQuery;
var
  Query: TFDQuery;
  SQL: string;
  TableName: string;
  StartPos, EndPos: Integer;
  DatabaseValue: string;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    raise Exception.Create('数据库连接对象未分配');

  if not FConnection.Connected then
    raise Exception.Create('数据库连接未建立');

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;

    // 设置格式选项
    Query.FormatOptions.MapRules.Clear;
    Query.FormatOptions.OwnMapRules := True;
    Query.FormatOptions.StrsEmpty2Null := False;
    Query.FormatOptions.StrsTrim := False;

    // 针对不同Access版本进行特殊处理，SQLite不需要特殊处理
    SQL := ASQL;

    try
      if IsAccess then
      begin
        // 处理SQLite特有的查询语句
        if (SQL <> '') and (Pos('sqlite_master', SQL) > 0) then
        begin
          if Pos('type=''table''', SQL) > 0 then
          begin
            // 查询表名 - 转换为Access查询
            try
              SQL := 'SELECT Name as name FROM MSysObjects WHERE Type=1 AND Flags=0';
            except
              // 如果系统表查询失败，使用标准查询
              SQL := 'SELECT TABLE_NAME as name FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE=''TABLE''';
            end;
          end
          else if Pos('type=''view''', SQL) > 0 then
          begin
            // 查询视图名 - 转换为Access查询
            try
              SQL := 'SELECT Name as name FROM MSysObjects WHERE Type=5 AND Flags=0';
            except
              // 如果系统表查询失败，使用标准查询
              SQL := 'SELECT TABLE_NAME as name FROM INFORMATION_SCHEMA.VIEWS';
            end;
          end
          else if Pos('type=''index''', SQL) > 0 then
          begin
            // 查询索引名 - 转换为Access查询
            try
              SQL := 'SELECT Name as name FROM MSysObjects WHERE Type=4 AND Flags=0';
            except
              // 如果系统表查询失败，使用标准查询
              SQL := 'SELECT INDEX_NAME as name FROM INFORMATION_SCHEMA.INDEXES';
            end;
          end
          else if Pos('type=''trigger''', SQL) > 0 then
          begin
            // Access不支持触发器，返回空结果
            SQL := 'SELECT ''dummy'' as name WHERE 1=0';
          end
          else
          begin
            // 其他情况返回空结果
            SQL := 'SELECT ''dummy'' as name WHERE 1=0';
          end;
        end
        else if (SQL <> '') and (Pos('PRAGMA', SQL) > 0) then
        begin
          // 处理PRAGMA语句
          if Pos('PRAGMA table_info', SQL) > 0 then
          begin
            // 提取表名
            StartPos := Pos('(', SQL);
            EndPos := Pos(')', SQL);

            if (StartPos > 0) and (EndPos > StartPos) then
            begin
              // 获取表名部分
              TableName := Copy(SQL, StartPos + 1, EndPos - StartPos - 1);

              // 清理引号字符
              TableName := StringReplace(TableName, '"', '', [rfReplaceAll]);
              TableName := StringReplace(TableName, '''', '', [rfReplaceAll]);

              // 检查表名是否有效
              if TableName = '' then
                TableName := 'dummy_table';

              // 转换为Access表结构查询 - 使用系统表
              try
                SQL := Format(
                  'SELECT COLUMN_NAME as name, DATA_TYPE as type, ' +
                  'CASE WHEN IS_NULLABLE = ''NO'' THEN 1 ELSE 0 END as notnull, ' +
                  'COLUMN_DEFAULT as dflt_value, ' +
                  'CASE WHEN COLUMN_NAME IN (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_NAME = ''%s'' AND CONSTRAINT_NAME LIKE ''%%PK%%'') THEN 1 ELSE 0 END as pk ' +
                  'FROM INFORMATION_SCHEMA.COLUMNS ' +
                  'WHERE TABLE_NAME = ''%s'' ' +
                  'ORDER BY ORDINAL_POSITION',
                  [TableName, TableName]);
              except
                // 如果系统表查询失败，使用简单查询
                SQL := Format('SELECT * FROM [%s] WHERE 1=0', [TableName]);
              end;
            end
            else
            begin
              // 如果无法提取表名，返回空结果
              SQL := 'SELECT ''dummy'' as name, ''text'' as type, 0 as notnull, '''' as dflt_value, 0 as pk WHERE 1=0';
            end;
          end
          else if Pos('PRAGMA database_list', SQL) > 0 then
          begin
            // 转换为Access数据库信息查询
            try
              DatabaseValue := FConnection.Params.Values['Database'];
              if DatabaseValue = '' then
                DatabaseValue := 'unknown';

              SQL := Format('SELECT 1 as seq, ''main'' as name, ''%s'' as file', [DatabaseValue]);
            except
              // 如果查询失败，返回默认的数据库信息
              SQL := 'SELECT 1 as seq, ''main'' as name, ''unknown'' as file';
            end;
          end
          else if Pos('PRAGMA compile_options', SQL) > 0 then
          begin
            // Access不支持编译选项，返回空结果
            SQL := 'SELECT ''dummy'' as value WHERE 1=0';
          end
          else
          begin
            // 其他PRAGMA语句返回空结果
            SQL := 'SELECT ''dummy'' as value WHERE 1=0';
          end;
        end;
      end;

      // 如果SQL为空，使用默认查询
      if SQL = '' then
        SQL := 'SELECT 1 as dummy';

      // 设置SQL语句并执行
      Query.SQL.Text := SQL;

      try
        Query.Open;
        Result := Query;
      except
        on E: Exception do
        begin
          // 如果查询失败，尝试使用备用查询
          if IsAccess then
          begin
            try
              Query.Close;
              Query.SQL.Text := 'SELECT 1 as dummy';
              Query.Open;
              Query.Close;

              // 使用简单的空结果查询作为备用
              Query.SQL.Text := 'SELECT ''dummy'' as name WHERE 1=0';
              Query.Open;
              Result := Query;
            except
              on E2: Exception do
              begin
                Query.Free;
                Query := nil;
                raise Exception.Create('查询执行失败: ' + E2.Message);
              end;
            end;
          end
          else
          begin
            Query.Free;
            Query := nil;
            raise Exception.Create('查询执行失败: ' + E.Message);
          end;
        end;
      end;
    except
      on E: Exception do
      begin
        if Assigned(Query) then
        begin
          Query.Free;
          Query := nil;
        end;
        raise Exception.Create('查询执行失败: ' + E.Message);
      end;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Query) then
      begin
        Query.Free;
        Query := nil;
      end;
      raise Exception.Create('查询执行失败: ' + E.Message);
    end;
  end;
end;

function TDBConnectionManager.GetTableNames: TStringList;
var
  Tables: TStringList;
  Query: TFDQuery;
begin
  Tables := TStringList.Create;

  try
    // 根据数据库类型加载表列表
    if IsSQLite then
    begin
      // 处理SQLite数据库的表查询
      try
        Query := TFDQuery.Create(nil);
        try
          Query.Connection := FConnection;
          Query.SQL.Text := 'SELECT name FROM sqlite_master WHERE type=''table'' ORDER BY name';
          Query.Open;

          while not Query.Eof do
          begin
            Tables.Add(Query.FieldByName('name').AsString);
            Query.Next;
          end;
        finally
          Query.Free;
        end;
      except
        // 忽略错误
      end;
    end
    else if IsAccess then
    begin
      // 处理Access数据库查询
      try
        Query := TFDQuery.Create(nil);
        try
          Query.Connection := FConnection;

          // 尝试多种不同的Access查询
          var SQLQueries: array[0..3] of string;
          SQLQueries[0] := 'SELECT Name FROM MSysObjects WHERE Type=1 AND Flags=0 AND Name NOT LIKE "MSys*" ORDER BY Name';
          SQLQueries[1] := 'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = ''BASE TABLE'' ORDER BY TABLE_NAME';
          SQLQueries[2] := 'SHOW TABLES';
          SQLQueries[3] := 'SELECT Name FROM MSysObjects WHERE Type=1 ORDER BY Name';

          var Success := False;
          for var i := 0 to High(SQLQueries) do
          begin
            try
              Query.Close;
              Query.SQL.Text := SQLQueries[i];
              Query.Open;

              while not Query.Eof do
              begin
                var TableName := '';
                if Query.FieldCount > 0 then
                begin
                  if Query.Fields[0].FieldName = 'Name' then
                    TableName := Query.FieldByName('Name').AsString
                  else if Query.Fields[0].FieldName = 'TABLE_NAME' then
                    TableName := Query.FieldByName('TABLE_NAME').AsString
                  else
                    TableName := Query.Fields[0].AsString;

                  // 过滤系统表
                  if (TableName <> '') and
                     (not TableName.StartsWith('MSys')) and
                     (not TableName.StartsWith('~')) then
                  begin
                    Tables.Add(TableName);
                  end;
                end;
                Query.Next;
              end;

              Success := True;
              Break; // 成功执行后退出循环
            except
              // 当前SQL失败，尝试下一个
              Continue;
            end;
          end;

          // 如果所有SQL都失败，尝试使用FireDAC的元数据功能
          if not Success then
          begin
            try
              FConnection.GetTableNames('', '', '', Tables);
            except
              // 元数据查询失败
            end;
          end;
        finally
          Query.Free;
        end;
      except
        on E: Exception do
        begin
          // 处理其他异常情况
          // 可以在这里添加日志记录
        end;
      end;
    end
    else if IsParadox then
    begin
      // 处理Paradox数据库文件查找
      try
        // 查找目录中的所有.db文件
        var SearchRec: TSearchRec;
        var Directory: string;

        Directory := FConnection.Params.Values['DefaultDir'];
        if Directory = '' then
          Directory := ExtractFilePath(FConnection.Params.Database);

        if (Directory <> '') and (Directory[Length(Directory)] <> PathDelim) then
          Directory := Directory + PathDelim;

        if FindFirst(Directory + '*.db', faAnyFile, SearchRec) = 0 then
        begin
          try
            repeat
              // 添加表名（去掉文件扩展名）
              Tables.Add(ChangeFileExt(SearchRec.Name, ''));
            until FindNext(SearchRec) <> 0;
          finally
            System.SysUtils.FindClose(SearchRec);
          end;
        end;
      except
        // 忽略错误
      end;
    end

  except
    // 如果获取表列表失败，清理资源
    if Assigned(Tables) then
      Tables.Clear;
  end;

  Result := Tables;
end;

procedure TDBConnectionManager.LoadTables;
var
  Query: TFDQuery;
  Node: TTreeNode;
begin
  if not Assigned(FTreeView) then
    Exit;

  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 加载视图列表
  try
    Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''table'' ORDER BY name');
    try
      while not Query.Eof do
      begin
        Node := FTreeView.Items.AddChildObject(
          FTreeView.Selected,
          Query.FieldByName('name').AsString,
          Pointer(1)); // 1表示视图类型

        // 设置图标
        Node.ImageIndex := 1; // 视图图标
        Node.SelectedIndex := 1;

        Query.Next;
      end;
    finally
      Query.Free;
    end;
  except
    on E: Exception do
    begin
      // 忽略错误
    end;
  end;
end;

procedure TDBConnectionManager.LoadViews;
var
  Query: TFDQuery;
  Node: TTreeNode;
begin
  if not Assigned(FTreeView) then
    Exit;

  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 加载索引列表
  try
    Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''view'' ORDER BY name');
    try
      while not Query.Eof do
      begin
        Node := FTreeView.Items.AddChildObject(
          FTreeView.Selected,
          Query.FieldByName('name').AsString,
          Pointer(2)); // 2表示索引类型

        // 设置图标
        Node.ImageIndex := 2; // 索引图标
        Node.SelectedIndex := 2;

        Query.Next;
      end;
    finally
      Query.Free;
    end;
  except
    on E: Exception do
    begin
      // 忽略错误
    end;
  end;
end;

procedure TDBConnectionManager.LoadIndices;
var
  Query: TFDQuery;
  Node: TTreeNode;
begin
  if not Assigned(FTreeView) then
    Exit;

  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 加载触发器列表
  try
    Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''index'' AND name NOT LIKE ''sqlite_%'' ORDER BY name');
    try
      while not Query.Eof do
      begin
        Node := FTreeView.Items.AddChildObject(
          FTreeView.Selected,
          Query.FieldByName('name').AsString,
          Pointer(3)); // 3表示触发器类型

        // 设置图标
        Node.ImageIndex := 3; // 触发器图标
        Node.SelectedIndex := 3;

        Query.Next;
      end;
    finally
      Query.Free;
    end;
  except
    on E: Exception do
    begin
      // 忽略错误
    end;
  end;
end;

procedure TDBConnectionManager.LoadTriggers;
var
  Query: TFDQuery;
  Node: TTreeNode;
begin
  if not Assigned(FTreeView) then
    Exit;

  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 获取表名列表字符串
  try
    Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''trigger'' ORDER BY name');
    try
      while not Query.Eof do
      begin
        Node := FTreeView.Items.AddChildObject(
          FTreeView.Selected,
          Query.FieldByName('name').AsString,
          Pointer(4)); // 4表示表名列表类型

        // 设置图标
        Node.ImageIndex := 4; // 表名列表图标
        Node.SelectedIndex := 4;

        Query.Next;
      end;
    finally
      Query.Free;
    end;
  except
    on E: Exception do
    begin
      // 忽略错误
    end;
  end;
end;

function TDBConnectionManager.GetTableStructure(const ATableName: string): TFDQuery;
begin
  Result := ExecuteQuery(Format('PRAGMA table_info(%s)', [ATableName]));
end;

function TDBConnectionManager.GetTableData(const ATableName: string): TFDQuery;
begin
  Result := ExecuteQuery(Format('SELECT * FROM %s', [ATableName]));
end;

function TDBConnectionManager.ExecuteSQL(const ASQL: string): TFDQuery;
begin
  Result := ExecuteQuery(ASQL);
end;

function TDBConnectionManager.GetDatabaseInfo: TStringList;
var
  Info: TStringList;
  Query: TFDQuery;
begin
  Info := TStringList.Create;

  try
    // 获取数据库文件信息
    Query := ExecuteQuery('PRAGMA database_list');
    try
      while not Query.Eof do
      begin
        Info.Add(Format(string('数据库文件: %s'), [Query.FieldByName('file').AsString]));
        Query.Next;
      end;
    finally
      Query.Free;
    end;

    // 获取SQLite版本信息
    Query := ExecuteQuery('SELECT sqlite_version() as version');
    try
      if not Query.Eof then
        Info.Add(Format('SQLite版本: %s', [Query.FieldByName('version').AsString]));
    finally
      Query.Free;
    end;

    // 获取编译选项
    Query := ExecuteQuery('PRAGMA compile_options');
    try
      Info.Add('编译选项:');
      while not Query.Eof do
      begin
        Info.Add('  ' + Query.FieldByName('value').AsString);
        Query.Next;
      end;
    finally
      Query.Free;
    end;
  except
    on E: Exception do
    begin
      Info.Add('获取数据库信息失败: ' + E.Message);
    end;
  end;

  Result := Info;
end;

function TDBConnectionManager.ConnectToExcelFile(const AFilePath: string): Boolean;
begin
  Result := False;
  FLastError := '';

  try
    if not FileExists(AFilePath) then
    begin
      FLastError := '文件不存在: ' + AFilePath;
      Exit;
    end;

    if not SameText(ExtractFileExt(AFilePath), '.xls') then
    begin
      FLastError := '只支持XLS格式的Excel文件';
      Exit;
    end;

    // 使用UnifiedExcelProcessor验证文件格式
    if not TExcelAPI.IsValidExcelFile(AFilePath) then
    begin
      FLastError := '无效的XLS文件格式';
      Exit;
    end;

    // 连接成功
    FCurrentExcelFile := AFilePath;
    FDBType := dbtExcel;
    FConnectionInfo := '数据源: Excel文件 (' + ExtractFileName(AFilePath) + ')';
    Result := True;

  except
    on E: Exception do
    begin
      FLastError := '连接Excel文件失败: ' + E.Message;
      Result := False;
    end;
  end;
end;

function TDBConnectionManager.GetExcelSheetNames: TStringList;
begin
  Result := nil;
  FLastError := '';

  try
    if not IsExcel then
    begin
      FLastError := '当前连接不是Excel文件';
      Exit;
    end;

    if FCurrentExcelFile = '' then
    begin
      FLastError := '没有打开的Excel文件';
      Exit;
    end;

    Result := TExcelAPI.GetSheetNames(FCurrentExcelFile);
  except
    on E: Exception do
    begin
      FLastError := '获取Excel工作表名称失败: ' + E.Message;
      Result := nil;
    end;
  end;
end;

function TDBConnectionManager.LoadExcelSheetData(const ASheetName: string): TFDMemTable;
begin
  Result := nil;
  FLastError := '';

  try
    if not IsExcel then
    begin
      FLastError := '当前连接不是Excel文件';
      Exit;
    end;

    if FCurrentExcelFile = '' then
    begin
      FLastError := '没有打开的Excel文件';
      Exit;
    end;

    Result := TExcelAPI.ReadSheetToDataSet(FCurrentExcelFile, ASheetName);
    if not Assigned(Result) then
    begin
      FLastError := '无法读取工作表数据: ' + ASheetName;
    end;
  except
    on E: Exception do
    begin
      FLastError := '加载Excel工作表数据失败: ' + E.Message;
      Result := nil;
    end;
  end;
end;

procedure TDBConnectionManager.DisconnectFromExcel;
begin
  try
    if IsExcel then
    begin
      FCurrentExcelFile := '';
      FDBType := dbtSQLite; // 恢复默认类型
      FConnectionInfo := string('数据源: 未连接');
    end;
  except
    on E: Exception do
      FLastError := '断开Excel连接失败: ' + E.Message;
  end;
end;

function TDBConnectionManager.WriteCSVFile(const FileName: string; Data: TFDMemTable; HasHeader: Boolean): Boolean;
var
  StringList: TStringList;
  i: Integer;
  Line: string;
begin
  Result := False;

  try
    if not Assigned(Data) then
      Exit;

    StringList := TStringList.Create;
    try
      // 写入表头行
      if HasHeader then
      begin
        Line := '';
        for i := 0 to Data.FieldCount - 1 do
        begin
          if i > 0 then Line := Line + ',';
          Line := Line + '"' + Data.Fields[i].FieldName + '"';
        end;
        StringList.Add(Line);
      end;

      // 写入数据行
      Data.First;
      while not Data.Eof do
      begin
        Line := '';
        for i := 0 to Data.FieldCount - 1 do
        begin
          if i > 0 then Line := Line + ',';
          Line := Line + '"' + Data.Fields[i].AsString + '"';
        end;
        StringList.Add(Line);
        Data.Next;
      end;

      // 保存到文件
      StringList.SaveToFile(FileName, TEncoding.UTF8);
      Result := True;
    finally
      StringList.Free;
    end;
  except
    on E: Exception do
    begin
      FLastError := '写入CSV文件失败: ' + E.Message;
      Result := False;
    end;
  end;
end;

// 统一的数据库连接方法
function TDBConnectionManager.ConnectToDatabase(const APath, AName: string; ADBType: TDBType): Boolean;
begin
  FLastError := '';

  try
    // 如果已连接则先断开
    if FConnection.Connected then
      FConnection.Close;

    // 完全清空连接参数并重新设置，确保没有之前的参数残留
    FConnection.Params.Clear;

    // 清除所有格式选项，避免不同数据库类型间的冲突
    FConnection.FormatOptions.MapRules.Clear;
    FConnection.FormatOptions.OwnMapRules := True;
    FConnection.FormatOptions.StrsEmpty2Null := False;
    FConnection.FormatOptions.StrsTrim := False;

    // 清除之前的Excel状态和数据库类型
    FCurrentExcelFile := '';
    FDBType := ADBType; // 设置为新的数据库类型

    // 重置连接信息
    FConnectionInfo := '';

    case ADBType of
      dbtSQLite:
      begin
        FConnection.Params.DriverID := 'SQLite';
        FConnection.Params.Database := APath;
        FConnection.Params.Add('CharacterSet=gb2312');
      end;

      dbtAccess:
      begin
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Add('DriverName=Microsoft Access Driver (*.mdb, *.accdb)');
        FConnection.Params.Add('Database=' + APath);
        FConnection.Params.Add('CharacterSet=gb2312');
        FConnection.Params.Add('ExtendedAnsiSQL=1');
        FConnection.Params.Add('SystemDB=');
        FConnection.Params.Add('Exclusive=0');
        FConnection.Params.Add('ReadOnly=0');
        FConnection.ResourceOptions.AutoConnect := True;
        FConnection.ResourceOptions.KeepConnection := True;
      end;

      dbtParadox, dbtParadoxDirect:
      begin
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Values['DriverName'] := 'Microsoft dBASE Driver (*.dbf)';
        FConnection.Params.Values['DefaultDir'] := APath;
      end;

      dbtExcel:
      begin
        // Excel文件类型：不使用FireDAC连接，只记录路径信息
        FConnection.Params.Clear;
        FConnection.Params.Add('ExcelMode=1');
        FConnection.Params.Add('ExcelPath=' + APath);
        FCurrentExcelFile := APath;
        Result := True;
        Exit;
      end;
    end;

    // 打开连接
    FConnection.Open;

    // 连接成功后，重新检测数据库类型以确保状态一致
    RefreshDBTypeDetection;

    Result := True;

  except
    on E: Exception do
    begin
      Result := False;
      FLastError := '数据库连接失败: ' + E.Message + ' (路径: ' + APath + ')';

      // 连接失败时，重置数据库类型和连接信息
      FDBType := dbtSQLite; // 默认类型
      FConnectionInfo := '数据源: 连接失败';
    end;
  end;
end;

// 断开数据库连接
procedure TDBConnectionManager.DisconnectFromDatabase;
begin
  if FConnection.Connected then
    FConnection.Close;

  // 清理Excel模式参数，确保不影响后续连接
  if FDBType = dbtExcel then
    FConnection.Params.Clear;

  FCurrentExcelFile := '';
  FDBType := dbtSQLite; // 重置为默认类型
  FConnectionInfo := '数据源: 已断开连接';
end;

// 重新检测数据库类型
procedure TDBConnectionManager.RefreshDBTypeDetection;
var
  DriverName, ConnectionString, DatabasePath, FileExt: string;
begin
  // 根据当前连接参数重新确定数据库类型
  if Assigned(FConnection) then
  begin
    // 首先检查是否是Excel模式（优先级最高）
    if FConnection.Params.Values['ExcelMode'] = '1' then
    begin
      FDBType := dbtExcel;
      FConnectionInfo := '数据源: Excel文件 (XLS格式)';
      Exit;
    end;

    // 获取数据库路径用于文件扩展名检测
    DatabasePath := FConnection.Params.Database;
    if DatabasePath = '' then
      DatabasePath := FConnection.Params.Values['ExcelPath'];
    if DatabasePath <> '' then
      FileExt := LowerCase(ExtractFileExt(DatabasePath));

    // 根据DriverID检测数据库类型
    if FConnection.Params.DriverID = 'SQLite' then
    begin
      FDBType := dbtSQLite;
      FConnectionInfo := '数据源: FireDAC SQLite 数据库驱动';
    end
    else if FConnection.Params.DriverID = 'MSAcc' then
    begin
      FDBType := dbtAccess;
      FConnectionInfo := '数据源: FireDAC MSAcc 数据库驱动 (Microsoft Access)';
    end
    else if FConnection.Params.DriverID = 'ODBC' then
    begin
      // 通过ODBC驱动名称和连接字符串判断具体的数据库类型
      DriverName := FConnection.Params.Values['DriverName'];
      ConnectionString := FConnection.Params.Values['ConnectionString'];

      if (Pos('Access', DriverName) > 0) or (Pos('*.mdb', DriverName) > 0) or (Pos('*.accdb', DriverName) > 0) then
      begin
        FDBType := dbtAccess;
        FConnectionInfo := '数据源: ODBC 驱动 (Microsoft Access)';
      end
      else if (Pos('Paradox', DriverName) > 0) or (Pos('*.db', DriverName) > 0) or (Pos('dBASE', DriverName) > 0) then
      begin
        FDBType := dbtParadox;
        FConnectionInfo := '数据源: ODBC 驱动 (Paradox)';
      end
      // 如果驱动名称中没有找到，再检查连接字符串
      else if (Pos('Paradox', ConnectionString) > 0) or (Pos('*.db', ConnectionString) > 0) then
      begin
        FDBType := dbtParadox;
        FConnectionInfo := '数据源: ODBC 驱动 (Paradox)';
      end
      // 如果还是无法确定，尝试通过文件扩展名判断
      else if FileExt <> '' then
      begin
        if (FileExt = '.mdb') or (FileExt = '.accdb') then
        begin
          FDBType := dbtAccess;
          FConnectionInfo := '数据源: ODBC 驱动 (Microsoft Access - 通过文件扩展名检测)';
        end
        else if (FileExt = '.db') or (FileExt = '.dbf') then
        begin
          FDBType := dbtParadox;
          FConnectionInfo := '数据源: ODBC 驱动 (Paradox - 通过文件扩展名检测)';
        end
        else
        begin
          FDBType := dbtSQLite; // 默认SQLite
          FConnectionInfo := '数据源: ODBC 驱动 (未知类型，默认SQLite)';
        end;
      end
      else
      begin
        FDBType := dbtSQLite; // 默认SQLite
        FConnectionInfo := '数据源: ODBC 驱动 (未知类型，默认SQLite)';
      end;
    end
    else if FConnection.Params.DriverID = '' then
    begin
      // 如果没有设置DriverID，尝试通过文件扩展名判断
      if FileExt <> '' then
      begin
        if (FileExt = '.db') or (FileExt = '.sqlite') or (FileExt = '.sqlite3') then
        begin
          FDBType := dbtSQLite;
          FConnectionInfo := '数据源: SQLite (通过文件扩展名检测)';
        end
        else if (FileExt = '.mdb') or (FileExt = '.accdb') then
        begin
          FDBType := dbtAccess;
          FConnectionInfo := '数据源: Microsoft Access (通过文件扩展名检测)';
        end
        else if (FileExt = '.xls') or (FileExt = '.xlsx') then
        begin
          FDBType := dbtExcel;
          FConnectionInfo := '数据源: Excel文件 (通过文件扩展名检测)';
        end
        else
        begin
          FDBType := dbtSQLite; // 默认SQLite
          FConnectionInfo := '数据源: 未设置驱动类型 (默认SQLite)';
        end;
      end
      else
      begin
        FDBType := dbtSQLite; // 默认SQLite
        FConnectionInfo := '数据源: 未设置驱动类型';
      end;
    end
    else
    begin
      FDBType := dbtSQLite; // 默认SQLite
      FConnectionInfo := '数据源: 未知驱动类型 (' + FConnection.Params.DriverID + ')';
    end;
  end
  else
  begin
    FDBType := dbtSQLite; // 默认SQLite
    FConnectionInfo := '数据源: 连接对象未分配';
  end;
end;



end.
